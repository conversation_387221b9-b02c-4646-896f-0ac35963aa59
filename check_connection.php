<?php
/**
 * Simple Database Connection Checker
 * 
 * This script helps diagnose database connection issues
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Database Connection Checker</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .code { background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; }
</style>";

// Test 1: Check if MySQL extension is loaded
echo "<div class='section'>";
echo "<h2>Test 1: PHP MySQL Extension</h2>";
if (extension_loaded('mysqli')) {
    echo "<p class='success'>✅ MySQLi extension is loaded</p>";
} else {
    echo "<p class='error'>❌ MySQLi extension is not loaded</p>";
    echo "<p class='info'>Please enable mysqli extension in php.ini</p>";
}
echo "</div>";

// Test 2: Check XAMPP status
echo "<div class='section'>";
echo "<h2>Test 2: XAMPP Services</h2>";
echo "<p class='info'>Please ensure the following services are running in XAMPP Control Panel:</p>";
echo "<ul>";
echo "<li>Apache (for web server)</li>";
echo "<li>MySQL (for database)</li>";
echo "</ul>";
echo "</div>";

// Test 3: Try different connection methods
echo "<div class='section'>";
echo "<h2>Test 3: Connection Attempts</h2>";

$connection_configs = [
    ['host' => 'localhost', 'user' => 'root', 'pass' => '', 'desc' => 'Default XAMPP (no password)'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'root', 'desc' => 'Common alternative (password: root)'],
    ['host' => '127.0.0.1', 'user' => 'root', 'pass' => '', 'desc' => 'IP address instead of localhost'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'mysql', 'desc' => 'Another common password'],
];

foreach ($connection_configs as $config) {
    echo "<h3>Trying: {$config['desc']}</h3>";
    
    $conn = @new mysqli($config['host'], $config['user'], $config['pass']);
    
    if ($conn->connect_error) {
        echo "<p class='error'>❌ Failed: " . $conn->connect_error . "</p>";
    } else {
        echo "<p class='success'>✅ Connection successful!</p>";
        
        // Check if banking_system database exists
        $result = $conn->query("SHOW DATABASES LIKE 'banking_system'");
        if ($result && $result->num_rows > 0) {
            echo "<p class='success'>✅ banking_system database exists</p>";
        } else {
            echo "<p class='warning'>⚠️ banking_system database does not exist</p>";
        }
        
        // Show all databases
        $result = $conn->query("SHOW DATABASES");
        if ($result) {
            echo "<p class='info'>Available databases:</p>";
            echo "<ul>";
            while ($row = $result->fetch_row()) {
                echo "<li>" . $row[0] . "</li>";
            }
            echo "</ul>";
        }
        
        $conn->close();
        
        // If this connection worked, update the config file
        updateConfigFile($config['host'], $config['user'], $config['pass']);
        break;
    }
}
echo "</div>";

// Test 4: Check config file
echo "<div class='section'>";
echo "<h2>Test 4: Configuration File</h2>";

if (file_exists('config/db_connect.php')) {
    echo "<p class='success'>✅ config/db_connect.php exists</p>";
    
    $config_content = file_get_contents('config/db_connect.php');
    echo "<p class='info'>Current configuration:</p>";
    echo "<div class='code'>" . htmlspecialchars($config_content) . "</div>";
} else {
    echo "<p class='error'>❌ config/db_connect.php not found</p>";
    echo "<p class='info'>Creating default configuration file...</p>";
    
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    $default_config = '<?php
// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system";

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set character set
$conn->set_charset("utf8mb4");
?>';
    
    file_put_contents('config/db_connect.php', $default_config);
    echo "<p class='success'>✅ Default configuration file created</p>";
}
echo "</div>";

// Test 5: Recommendations
echo "<div class='section'>";
echo "<h2>Recommendations</h2>";

echo "<h3>If MySQL connection is still failing:</h3>";
echo "<ol>";
echo "<li><strong>Check XAMPP Control Panel:</strong>";
echo "<ul>";
echo "<li>Make sure MySQL service is started (green 'Running' status)</li>";
echo "<li>If it's not running, click 'Start' button</li>";
echo "<li>Check for any error messages in the XAMPP logs</li>";
echo "</ul></li>";

echo "<li><strong>Try restarting XAMPP services:</strong>";
echo "<ul>";
echo "<li>Stop both Apache and MySQL</li>";
echo "<li>Wait a few seconds</li>";
echo "<li>Start MySQL first, then Apache</li>";
echo "</ul></li>";

echo "<li><strong>Check Windows Services (if on Windows):</strong>";
echo "<ul>";
echo "<li>Open Services (services.msc)</li>";
echo "<li>Look for 'MySQL' service</li>";
echo "<li>Make sure it's running</li>";
echo "</ul></li>";

echo "<li><strong>Reset MySQL password:</strong>";
echo "<div class='code'>";
echo "1. Stop MySQL in XAMPP<br>";
echo "2. Open command prompt as administrator<br>";
echo "3. Navigate to XAMPP MySQL bin folder<br>";
echo "4. Run: mysqld --skip-grant-tables<br>";
echo "5. In another command prompt: mysql -u root<br>";
echo "6. Run: UPDATE mysql.user SET Password=PASSWORD('') WHERE User='root';<br>";
echo "7. Run: FLUSH PRIVILEGES;<br>";
echo "8. Restart MySQL in XAMPP";
echo "</div></li>";
echo "</ol>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Once MySQL is working, visit: <a href='setup_system.php'>setup_system.php</a></li>";
echo "<li>Choose to create the banking system database</li>";
echo "<li>Test the system with: <a href='test_consolidated_database.php'>test_consolidated_database.php</a></li>";
echo "<li>Access the banking system: <a href='index.php'>index.php</a></li>";
echo "</ol>";
echo "</div>";

function updateConfigFile($host, $user, $pass) {
    $config_content = "<?php
// Database connection parameters
\$host = \"$host\";
\$username = \"$user\";
\$password = \"$pass\";
\$database = \"banking_system\";

// Create connection
\$conn = new mysqli(\$host, \$username, \$password, \$database);

// Check connection
if (\$conn->connect_error) {
    die(\"Connection failed: \" . \$conn->connect_error);
}

// Set character set
\$conn->set_charset(\"utf8mb4\");
?>";

    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    file_put_contents('config/db_connect.php', $config_content);
    echo "<p class='success'>✅ Updated config/db_connect.php with working credentials</p>";
}
?>
