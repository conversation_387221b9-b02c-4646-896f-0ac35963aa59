<?php
/**
 * Simple MySQL Connection Test
 */

echo "<h1>MySQL Connection Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Test different connection scenarios
$configs = [
    ['host' => 'localhost', 'user' => 'root', 'pass' => '', 'desc' => 'Default XAMPP (no password)'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'root', 'desc' => 'Password: root'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'mysql', 'desc' => 'Password: mysql'],
    ['host' => '127.0.0.1', 'user' => 'root', 'pass' => '', 'desc' => 'Using IP address'],
];

$working_config = null;

foreach ($configs as $config) {
    echo "<h3>Testing: {$config['desc']}</h3>";
    
    $conn = @new mysqli($config['host'], $config['user'], $config['pass']);
    
    if ($conn->connect_error) {
        echo "<p class='error'>❌ Failed: " . $conn->connect_error . "</p>";
    } else {
        echo "<p class='success'>✅ Connection successful!</p>";
        $working_config = $config;
        
        // Check if banking_system database exists
        $result = $conn->query("SHOW DATABASES LIKE 'banking_system'");
        if ($result && $result->num_rows > 0) {
            echo "<p class='success'>✅ banking_system database exists</p>";
        } else {
            echo "<p class='info'>ℹ️ banking_system database does not exist - needs to be created</p>";
        }
        
        $conn->close();
        break; // Stop at first working connection
    }
}

if ($working_config) {
    echo "<h2>Solution Found!</h2>";
    echo "<p class='success'>Working configuration: {$working_config['desc']}</p>";
    
    // Update the config file
    $new_config = "<?php
// Database connection parameters
\$host = \"{$working_config['host']}\";
\$username = \"{$working_config['user']}\";
\$password = \"{$working_config['pass']}\";
\$database = \"banking_system\";

// Create connection
\$conn = new mysqli(\$host, \$username, \$password, \$database);

// Check connection
if (\$conn->connect_error) {
    die(\"Connection failed: \" . \$conn->connect_error);
}

// Set character set
\$conn->set_charset(\"utf8mb4\");
?>";

    file_put_contents('config/db_connect.php', $new_config);
    echo "<p class='success'>✅ Updated config/db_connect.php with working credentials</p>";
    
    // Now try to create the database if it doesn't exist
    $conn = new mysqli($working_config['host'], $working_config['user'], $working_config['pass']);
    
    $result = $conn->query("SHOW DATABASES LIKE 'banking_system'");
    if (!$result || $result->num_rows == 0) {
        echo "<h3>Creating banking_system database...</h3>";
        
        if ($conn->query("CREATE DATABASE banking_system")) {
            echo "<p class='success'>✅ Database created successfully!</p>";
            
            // Now run the setup script
            $conn->select_db("banking_system");
            
            if (file_exists('setup_database.sql')) {
                $sql_content = file_get_contents('setup_database.sql');
                $statements = explode(';', $sql_content);
                
                $success = true;
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        if (!$conn->query($statement)) {
                            echo "<p class='error'>Error: " . $conn->error . "</p>";
                            $success = false;
                        }
                    }
                }
                
                if ($success) {
                    echo "<p class='success'>✅ Database setup completed!</p>";
                    echo "<p class='info'>You can now access the banking system: <a href='index.php'>index.php</a></p>";
                }
            }
        } else {
            echo "<p class='error'>❌ Failed to create database: " . $conn->error . "</p>";
        }
    }
    
    $conn->close();
    
} else {
    echo "<h2>No Working Connection Found</h2>";
    echo "<p class='error'>Please check the following:</p>";
    echo "<ol>";
    echo "<li>XAMPP Control Panel - MySQL service is running</li>";
    echo "<li>No other applications using port 3306</li>";
    echo "<li>Try restarting XAMPP as Administrator</li>";
    echo "<li>Check Windows Services for MySQL</li>";
    echo "</ol>";
}

echo "<h2>XAMPP Troubleshooting</h2>";
echo "<p class='info'><strong>Common Solutions:</strong></p>";
echo "<ul>";
echo "<li><strong>Port 3306 in use:</strong> Change MySQL port in XAMPP config</li>";
echo "<li><strong>Permission issues:</strong> Run XAMPP as Administrator</li>";
echo "<li><strong>Service won't start:</strong> Check Windows Services for conflicting MySQL installations</li>";
echo "<li><strong>Password issues:</strong> Reset MySQL root password using steps above</li>";
echo "</ul>";

echo "<p class='info'><strong>Default Login Credentials (once working):</strong></p>";
echo "<ul>";
echo "<li>Customer: <EMAIL> / 09009090909999</li>";
echo "<li>Admin: admin / admin123</li>";
echo "</ul>";
?>
