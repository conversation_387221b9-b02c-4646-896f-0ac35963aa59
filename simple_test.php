<?php
echo "<h1>Simple MySQL Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;}</style>";

echo "<h2>PHP MySQL Extension Check:</h2>";
if (extension_loaded('mysqli')) {
    echo "<p class='success'>✅ MySQLi extension is loaded</p>";
} else {
    echo "<p class='error'>❌ MySQLi extension is NOT loaded</p>";
    exit();
}

echo "<h2>Basic Connection Test:</h2>";

// Try the most basic connection
$connection = @mysqli_connect('localhost', 'root', '');

if ($connection) {
    echo "<p class='success'>✅ MySQL connection successful!</p>";
    
    // Show MySQL version
    $version = mysqli_get_server_info($connection);
    echo "<p>MySQL Version: $version</p>";
    
    // Show databases
    $result = mysqli_query($connection, "SHOW DATABASES");
    echo "<h3>Available Databases:</h3><ul>";
    while ($row = mysqli_fetch_row($result)) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
    
    mysqli_close($connection);
    
    echo "<h2>Next Steps:</h2>";
    echo "<p>1. Create 'banking_system' database in phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></p>";
    echo "<p>2. Import setup_database.sql file</p>";
    echo "<p>3. Try accessing the banking system: <a href='index.php'>index.php</a></p>";
    
} else {
    echo "<p class='error'>❌ MySQL connection failed!</p>";
    echo "<p>Error: " . mysqli_connect_error() . "</p>";
    
    echo "<h2>Troubleshooting:</h2>";
    echo "<ol>";
    echo "<li><strong>Check XAMPP Control Panel:</strong> MySQL should show 'Running' status</li>";
    echo "<li><strong>Run XAMPP as Administrator</strong></li>";
    echo "<li><strong>Check port 3306:</strong> Another MySQL service might be using it</li>";
    echo "<li><strong>Restart computer</strong> and try again</li>";
    echo "</ol>";
    
    echo "<h3>Manual MySQL Start:</h3>";
    echo "<p>1. Open Command Prompt as Administrator</p>";
    echo "<p>2. Navigate to: <code>cd C:\\xampp\\mysql\\bin</code></p>";
    echo "<p>3. Run: <code>mysqld --console</code></p>";
    echo "<p>4. Look for any error messages</p>";
}
?>
