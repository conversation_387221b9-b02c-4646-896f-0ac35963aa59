-- =====================================================
-- CONSOLIDATED BANKING SYSTEM DATABASE SCHEMA
-- =====================================================
-- This file contains the complete, unified database schema
-- for the Ferrer Banking System, consolidating all tables,
-- relationships, constraints, and security features.
--
-- Version: 2.0
-- Created: 2025-02-07
-- Author: Database Consolidation Script
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- DATABASE CREATION
-- =====================================================

CREATE DATABASE IF NOT EXISTS `banking_system`
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_general_ci;

USE `banking_system`;

-- =====================================================
-- TABLE: branch
-- Purpose: Store bank branch information
-- Relationships: Referenced by account, pending_account
-- =====================================================

DROP TABLE IF EXISTS `branch`;
CREATE TABLE `branch` (
  `BranchID` int(11) NOT NULL AUTO_INCREMENT,
  `BranchName` varchar(100) NOT NULL,
  `Location` varchar(255) NOT NULL,
  `BranchCode` varchar(10) UNIQUE,
  `Phone` varchar(15),
  `Email` varchar(100),
  `Manager` varchar(100),
  `Status` enum('active', 'inactive', 'maintenance') NOT NULL DEFAULT 'active',
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`BranchID`),
  INDEX `idx_branch_status` (`Status`),
  INDEX `idx_branch_code` (`BranchCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: customer
-- Purpose: Store customer information and account status
-- Relationships: Referenced by account, pending_account, email_log, verification_log
-- Security: Email verification, status tracking, audit trail
-- =====================================================

DROP TABLE IF EXISTS `customer`;
CREATE TABLE `customer` (
  `CustomerID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL,
  `Phone` varchar(15) NOT NULL,
  `Email` varchar(100) NOT NULL UNIQUE,
  `Address` text NOT NULL,
  `DateOfBirth` date,
  `NationalID` varchar(50) UNIQUE,
  `Status` enum('pending', 'active', 'suspended', 'closed') NOT NULL DEFAULT 'pending',
  `VerificationCode` varchar(32) DEFAULT NULL,
  `VerificationExpiry` datetime DEFAULT NULL,
  `IsVerified` tinyint(1) NOT NULL DEFAULT 0,
  `RegistrationDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `LastLoginDate` datetime DEFAULT NULL,
  `FailedLoginAttempts` int(3) DEFAULT 0,
  `AccountLockUntil` datetime DEFAULT NULL,
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`CustomerID`),
  UNIQUE KEY `unique_email` (`Email`),
  UNIQUE KEY `unique_phone` (`Phone`),
  UNIQUE KEY `unique_national_id` (`NationalID`),
  INDEX `idx_customer_status` (`Status`),
  INDEX `idx_customer_email` (`Email`),
  INDEX `idx_customer_verification` (`IsVerified`),
  INDEX `idx_customer_registration` (`RegistrationDate`),
  CONSTRAINT `chk_customer_phone` CHECK (CHAR_LENGTH(`Phone`) >= 10),
  CONSTRAINT `chk_customer_email` CHECK (`Email` REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: account
-- Purpose: Store customer account information
-- Relationships: References customer, branch; Referenced by transaction
-- Security: Balance constraints, account type validation
-- =====================================================

DROP TABLE IF EXISTS `account`;
CREATE TABLE `account` (
  `AccountID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) NOT NULL,
  `BranchID` int(11) NOT NULL,
  `AccountNumber` varchar(20) NOT NULL UNIQUE,
  `AccountType` enum('Savings', 'Checking', 'Investment', 'Business', 'Student') NOT NULL DEFAULT 'Savings',
  `Balance` decimal(15,2) NOT NULL DEFAULT 0.00,
  `MinimumBalance` decimal(15,2) NOT NULL DEFAULT 100.00,
  `InterestRate` decimal(5,4) DEFAULT 0.0000,
  `Status` enum('active', 'inactive', 'frozen', 'closed') NOT NULL DEFAULT 'active',
  `OpenDate` date NOT NULL DEFAULT (CURRENT_DATE),
  `CloseDate` date DEFAULT NULL,
  `LastTransactionDate` datetime DEFAULT NULL,
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`AccountID`),
  UNIQUE KEY `unique_account_number` (`AccountNumber`),
  KEY `idx_customer_id` (`CustomerID`),
  KEY `idx_branch_id` (`BranchID`),
  KEY `idx_account_type` (`AccountType`),
  KEY `idx_account_status` (`Status`),
  KEY `idx_balance` (`Balance`),
  CONSTRAINT `chk_balance_positive` CHECK (`Balance` >= 0),
  CONSTRAINT `chk_minimum_balance_positive` CHECK (`MinimumBalance` >= 0),
  CONSTRAINT `chk_interest_rate_valid` CHECK (`InterestRate` >= 0 AND `InterestRate` <= 1),
  CONSTRAINT `chk_close_date_after_open` CHECK (`CloseDate` IS NULL OR `CloseDate` >= `OpenDate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: transaction
-- Purpose: Store all financial transactions
-- Relationships: References account
-- Security: Amount validation, transaction type constraints, audit trail
-- =====================================================

DROP TABLE IF EXISTS `transaction`;
CREATE TABLE `transaction` (
  `TransactionID` int(11) NOT NULL AUTO_INCREMENT,
  `AccountID` int(11) NOT NULL,
  `TransactionNumber` varchar(30) NOT NULL UNIQUE,
  `Amount` decimal(15,2) NOT NULL,
  `Type` enum('Credit', 'Debit', 'Transfer', 'Deposit', 'Withdrawal', 'Interest', 'Fee') NOT NULL,
  `Description` varchar(255),
  `ReferenceNumber` varchar(50),
  `RelatedAccountID` int(11) DEFAULT NULL COMMENT 'For transfers - the other account involved',
  `Date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ProcessedBy` varchar(100) DEFAULT 'SYSTEM',
  `Status` enum('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'completed',
  `BalanceAfter` decimal(15,2) NOT NULL,
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`TransactionID`),
  UNIQUE KEY `unique_transaction_number` (`TransactionNumber`),
  KEY `idx_account_id` (`AccountID`),
  KEY `idx_related_account_id` (`RelatedAccountID`),
  KEY `idx_transaction_date` (`Date`),
  KEY `idx_transaction_type` (`Type`),
  KEY `idx_transaction_status` (`Status`),
  KEY `idx_amount` (`Amount`),
  CONSTRAINT `chk_amount_not_zero` CHECK (`Amount` != 0),
  CONSTRAINT `chk_balance_after_positive` CHECK (`BalanceAfter` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: admin
-- Purpose: Store administrator account information
-- Security: Password hashing, login tracking, role-based access
-- =====================================================

DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin` (
  `AdminID` int(11) NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) NOT NULL UNIQUE,
  `Password` varchar(255) NOT NULL,
  `Name` varchar(100) NOT NULL,
  `Email` varchar(100) NOT NULL UNIQUE,
  `Role` enum('super_admin', 'admin', 'manager', 'operator') NOT NULL DEFAULT 'admin',
  `Status` enum('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
  `LastLogin` datetime DEFAULT NULL,
  `LoginAttempts` int(3) DEFAULT 0,
  `LockUntil` datetime DEFAULT NULL,
  `PasswordChangedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`AdminID`),
  UNIQUE KEY `unique_username` (`Username`),
  UNIQUE KEY `unique_admin_email` (`Email`),
  INDEX `idx_admin_status` (`Status`),
  INDEX `idx_admin_role` (`Role`),
  CONSTRAINT `chk_admin_username_length` CHECK (CHAR_LENGTH(`Username`) >= 3),
  CONSTRAINT `chk_admin_password_length` CHECK (CHAR_LENGTH(`Password`) >= 8)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: pending_account
-- Purpose: Store new account requests awaiting approval
-- Relationships: References customer, branch
-- Security: Status tracking, approval workflow
-- =====================================================

DROP TABLE IF EXISTS `pending_account`;
CREATE TABLE `pending_account` (
  `RequestID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) NOT NULL,
  `AccountType` enum('Savings', 'Checking', 'Investment', 'Business', 'Student') NOT NULL DEFAULT 'Savings',
  `BranchID` int(11) NOT NULL,
  `InitialDeposit` decimal(15,2) NOT NULL DEFAULT 0.00,
  `RequestDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `Status` enum('pending', 'approved', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending',
  `ApprovedBy` int(11) DEFAULT NULL COMMENT 'AdminID who approved/rejected',
  `ApprovalDate` datetime DEFAULT NULL,
  `RejectionReason` text DEFAULT NULL,
  `Notes` text DEFAULT NULL,
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`RequestID`),
  KEY `idx_customer_id_pending` (`CustomerID`),
  KEY `idx_branch_id_pending` (`BranchID`),
  KEY `idx_approved_by` (`ApprovedBy`),
  KEY `idx_request_status` (`Status`),
  KEY `idx_request_date` (`RequestDate`),
  CONSTRAINT `chk_initial_deposit_positive` CHECK (`InitialDeposit` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: email_log
-- Purpose: Track email activities for audit and debugging
-- Relationships: References customer
-- Security: Audit trail for email communications
-- =====================================================

DROP TABLE IF EXISTS `email_log`;
CREATE TABLE `email_log` (
  `LogID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) DEFAULT NULL,
  `Email` varchar(255) NOT NULL,
  `Subject` varchar(255) NOT NULL,
  `Status` enum('success', 'error') NOT NULL,
  `ErrorMessage` text DEFAULT NULL,
  `IPAddress` varchar(45) DEFAULT NULL,
  `UserAgent` varchar(255) DEFAULT NULL,
  `EmailType` enum('verification', 'password_reset', 'notification', 'marketing') DEFAULT 'notification',
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`LogID`),
  KEY `idx_customer_id_email` (`CustomerID`),
  KEY `idx_email_status` (`Status`),
  KEY `idx_email_type` (`EmailType`),
  KEY `idx_email_created` (`CreatedAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: verification_log
-- Purpose: Track verification activities for security monitoring
-- Relationships: References customer
-- Security: Audit trail for verification attempts
-- =====================================================

DROP TABLE IF EXISTS `verification_log`;
CREATE TABLE `verification_log` (
  `LogID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) DEFAULT NULL,
  `Email` varchar(255) NOT NULL,
  `Action` enum('verify', 'resend', 'expire') NOT NULL,
  `Status` enum('success', 'error', 'info') NOT NULL,
  `IPAddress` varchar(45) DEFAULT NULL,
  `UserAgent` varchar(255) DEFAULT NULL,
  `VerificationCode` varchar(32) DEFAULT NULL,
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`LogID`),
  KEY `idx_customer_id_verification` (`CustomerID`),
  KEY `idx_verification_action` (`Action`),
  KEY `idx_verification_status` (`Status`),
  KEY `idx_verification_created` (`CreatedAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: audit_log
-- Purpose: Track all system activities for security and compliance
-- Security: Complete audit trail for regulatory compliance
-- =====================================================

DROP TABLE IF EXISTS `audit_log`;
CREATE TABLE `audit_log` (
  `AuditID` int(11) NOT NULL AUTO_INCREMENT,
  `TableName` varchar(50) NOT NULL,
  `RecordID` int(11) NOT NULL,
  `Action` enum('INSERT', 'UPDATE', 'DELETE') NOT NULL,
  `OldValues` json DEFAULT NULL,
  `NewValues` json DEFAULT NULL,
  `UserID` int(11) DEFAULT NULL COMMENT 'CustomerID or AdminID',
  `UserType` enum('customer', 'admin', 'system') NOT NULL DEFAULT 'system',
  `IPAddress` varchar(45) DEFAULT NULL,
  `UserAgent` varchar(255) DEFAULT NULL,
  `Timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`AuditID`),
  KEY `idx_audit_table` (`TableName`),
  KEY `idx_audit_record` (`RecordID`),
  KEY `idx_audit_action` (`Action`),
  KEY `idx_audit_user` (`UserID`, `UserType`),
  KEY `idx_audit_timestamp` (`Timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE: system_settings
-- Purpose: Store system configuration and parameters
-- Security: Centralized configuration management
-- =====================================================

DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `SettingID` int(11) NOT NULL AUTO_INCREMENT,
  `SettingKey` varchar(100) NOT NULL UNIQUE,
  `SettingValue` text NOT NULL,
  `SettingType` enum('string', 'integer', 'decimal', 'boolean', 'json') NOT NULL DEFAULT 'string',
  `Description` text DEFAULT NULL,
  `Category` varchar(50) DEFAULT 'general',
  `IsEditable` tinyint(1) NOT NULL DEFAULT 1,
  `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`SettingID`),
  UNIQUE KEY `unique_setting_key` (`SettingKey`),
  KEY `idx_setting_category` (`Category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Account table constraints
ALTER TABLE `account`
  ADD CONSTRAINT `fk_account_customer` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_account_branch` FOREIGN KEY (`BranchID`) REFERENCES `branch` (`BranchID`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Transaction table constraints
ALTER TABLE `transaction`
  ADD CONSTRAINT `fk_transaction_account` FOREIGN KEY (`AccountID`) REFERENCES `account` (`AccountID`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_transaction_related_account` FOREIGN KEY (`RelatedAccountID`) REFERENCES `account` (`AccountID`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Pending account table constraints
ALTER TABLE `pending_account`
  ADD CONSTRAINT `fk_pending_account_customer` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_pending_account_branch` FOREIGN KEY (`BranchID`) REFERENCES `branch` (`BranchID`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_pending_account_approved_by` FOREIGN KEY (`ApprovedBy`) REFERENCES `admin` (`AdminID`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Email log table constraints
ALTER TABLE `email_log`
  ADD CONSTRAINT `fk_email_log_customer` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Verification log table constraints
ALTER TABLE `verification_log`
  ADD CONSTRAINT `fk_verification_log_customer` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`) ON DELETE SET NULL ON UPDATE CASCADE;

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Additional performance indexes for common queries
CREATE INDEX `idx_customer_name` ON `customer` (`Name`);
CREATE INDEX `idx_account_balance_range` ON `account` (`Balance`, `AccountType`);
CREATE INDEX `idx_transaction_date_range` ON `transaction` (`Date`, `Type`, `Amount`);
CREATE INDEX `idx_transaction_account_date` ON `transaction` (`AccountID`, `Date` DESC);

-- =====================================================
-- INITIAL DATA INSERTION
-- =====================================================

-- Insert default system settings
INSERT INTO `system_settings` (`SettingKey`, `SettingValue`, `SettingType`, `Description`, `Category`) VALUES
('min_account_balance', '100.00', 'decimal', 'Minimum account balance required', 'account'),
('max_daily_withdrawal', '10000.00', 'decimal', 'Maximum daily withdrawal limit', 'transaction'),
('max_transfer_amount', '50000.00', 'decimal', 'Maximum single transfer amount', 'transaction'),
('verification_code_expiry', '24', 'integer', 'Verification code expiry in hours', 'security'),
('max_login_attempts', '5', 'integer', 'Maximum failed login attempts before lock', 'security'),
('account_lock_duration', '30', 'integer', 'Account lock duration in minutes', 'security'),
('interest_rate_savings', '0.0250', 'decimal', 'Default interest rate for savings accounts', 'account'),
('interest_rate_checking', '0.0100', 'decimal', 'Default interest rate for checking accounts', 'account'),
('system_maintenance_mode', 'false', 'boolean', 'System maintenance mode flag', 'system'),
('email_verification_required', 'true', 'boolean', 'Require email verification for new accounts', 'security');

-- Insert default admin user (password: admin123)
INSERT INTO `admin` (`Username`, `Password`, `Name`, `Email`, `Role`) VALUES
('admin', '$2y$10$qeS0HEh7urRAiMHxgVjkNu.lQR6zRsmRpDiZrPjVcNlOYHkLgPkry', 'System Administrator', '<EMAIL>', 'super_admin');

-- Insert branch data
INSERT INTO `branch` (`BranchID`, `BranchName`, `Location`, `BranchCode`, `Phone`, `Email`, `Manager`) VALUES
(1, 'Main Branch', 'Butuan', 'BTN001', '************', '<EMAIL>', 'Maria Santos'),
(2, 'Downtown Branch', 'Butuan City Center', 'BTN002', '************', '<EMAIL>', 'Juan Dela Cruz'),
(3, 'Sibagat Branch', 'Sibagat', 'SBG001', '************', '<EMAIL>', 'Ana Rodriguez');

-- Insert sample customer data
INSERT INTO `customer` (`CustomerID`, `Name`, `Phone`, `Email`, `Address`, `Status`, `IsVerified`, `RegistrationDate`) VALUES
(1, 'Wendelyn Ferrer', '**************', '<EMAIL>', 'Sibagat, Agusan del Sur', 'active', 1, '2025-01-01 10:00:00'),
(2, 'John Smith', '***********', '<EMAIL>', 'Butuan City, Agusan del Norte', 'active', 1, '2025-01-02 11:00:00'),
(3, 'Maria Garcia', '***********', '<EMAIL>', 'Ampayon, Butuan City', 'active', 1, '2025-01-03 12:00:00');

-- Generate account numbers and insert account data
INSERT INTO `account` (`AccountID`, `CustomerID`, `BranchID`, `AccountNumber`, `AccountType`, `Balance`, `MinimumBalance`, `InterestRate`, `OpenDate`) VALUES
(101, 1, 1, '1001-0000-0101', 'Savings', 5000.00, 100.00, 0.0250, '2025-01-01'),
(102, 1, 1, '1001-0000-0102', 'Checking', 3500.00, 50.00, 0.0100, '2025-01-01'),
(103, 2, 2, '2002-0000-0103', 'Savings', 7500.00, 100.00, 0.0250, '2025-01-02'),
(104, 2, 2, '2002-0000-0104', 'Investment', 15000.00, 1000.00, 0.0400, '2025-01-02'),
(105, 3, 3, '3003-0000-0105', 'Savings', 4200.00, 100.00, 0.0250, '2025-01-03');

-- Insert sample transaction data with proper transaction numbers
INSERT INTO `transaction` (`TransactionID`, `AccountID`, `TransactionNumber`, `Amount`, `Type`, `Description`, `Date`, `BalanceAfter`) VALUES
(1, 101, 'TXN-2025-0001', 1000.00, 'Deposit', 'Initial deposit', '2025-01-15 09:00:00', 1000.00),
(2, 101, 'TXN-2025-0002', 500.00, 'Withdrawal', 'ATM withdrawal', '2025-01-20 14:30:00', 500.00),
(3, 102, 'TXN-2025-0003', 1500.00, 'Deposit', 'Salary deposit', '2025-01-22 08:00:00', 1500.00),
(4, 101, 'TXN-2025-0004', 2000.00, 'Deposit', 'Cash deposit', '2025-01-25 10:15:00', 2500.00),
(5, 103, 'TXN-2025-0005', 1000.00, 'Deposit', 'Initial deposit', '2025-01-26 11:00:00', 1000.00),
(6, 103, 'TXN-2025-0006', -300.00, 'Withdrawal', 'ATM withdrawal', '2025-01-28 16:45:00', 700.00),
(7, 104, 'TXN-2025-0007', 5000.00, 'Deposit', 'Investment deposit', '2025-01-30 09:30:00', 5000.00),
(8, 105, 'TXN-2025-0008', 1200.00, 'Deposit', 'Initial deposit', '2025-02-01 10:00:00', 1200.00),
(9, 102, 'TXN-2025-0009', -800.00, 'Withdrawal', 'Bill payment', '2025-02-03 13:20:00', 700.00),
(10, 101, 'TXN-2025-0010', 1500.00, 'Transfer', 'Transfer from savings', '2025-02-05 15:00:00', 4000.00);

-- Update account balances to match final transaction balances
UPDATE `account` SET `Balance` = 5000.00, `LastTransactionDate` = '2025-02-05 15:00:00' WHERE `AccountID` = 101;
UPDATE `account` SET `Balance` = 3500.00, `LastTransactionDate` = '2025-02-03 13:20:00' WHERE `AccountID` = 102;
UPDATE `account` SET `Balance` = 7500.00, `LastTransactionDate` = '2025-01-28 16:45:00' WHERE `AccountID` = 103;
UPDATE `account` SET `Balance` = 15000.00, `LastTransactionDate` = '2025-01-30 09:30:00' WHERE `AccountID` = 104;
UPDATE `account` SET `Balance` = 4200.00, `LastTransactionDate` = '2025-02-01 10:00:00' WHERE `AccountID` = 105;

-- =====================================================
-- TRIGGERS FOR AUDIT LOGGING
-- =====================================================

-- Trigger for customer table audit
DELIMITER $$
CREATE TRIGGER `tr_customer_audit_insert` AFTER INSERT ON `customer`
FOR EACH ROW
BEGIN
    INSERT INTO `audit_log` (`TableName`, `RecordID`, `Action`, `NewValues`, `UserType`)
    VALUES ('customer', NEW.CustomerID, 'INSERT', JSON_OBJECT(
        'CustomerID', NEW.CustomerID,
        'Name', NEW.Name,
        'Email', NEW.Email,
        'Status', NEW.Status
    ), 'system');
END$$

CREATE TRIGGER `tr_customer_audit_update` AFTER UPDATE ON `customer`
FOR EACH ROW
BEGIN
    INSERT INTO `audit_log` (`TableName`, `RecordID`, `Action`, `OldValues`, `NewValues`, `UserType`)
    VALUES ('customer', NEW.CustomerID, 'UPDATE', JSON_OBJECT(
        'CustomerID', OLD.CustomerID,
        'Name', OLD.Name,
        'Email', OLD.Email,
        'Status', OLD.Status
    ), JSON_OBJECT(
        'CustomerID', NEW.CustomerID,
        'Name', NEW.Name,
        'Email', NEW.Email,
        'Status', NEW.Status
    ), 'system');
END$$

-- Trigger for account table audit
CREATE TRIGGER `tr_account_audit_insert` AFTER INSERT ON `account`
FOR EACH ROW
BEGIN
    INSERT INTO `audit_log` (`TableName`, `RecordID`, `Action`, `NewValues`, `UserType`)
    VALUES ('account', NEW.AccountID, 'INSERT', JSON_OBJECT(
        'AccountID', NEW.AccountID,
        'CustomerID', NEW.CustomerID,
        'AccountNumber', NEW.AccountNumber,
        'AccountType', NEW.AccountType,
        'Balance', NEW.Balance
    ), 'system');
END$$

CREATE TRIGGER `tr_account_audit_update` AFTER UPDATE ON `account`
FOR EACH ROW
BEGIN
    INSERT INTO `audit_log` (`TableName`, `RecordID`, `Action`, `OldValues`, `NewValues`, `UserType`)
    VALUES ('account', NEW.AccountID, 'UPDATE', JSON_OBJECT(
        'AccountID', OLD.AccountID,
        'Balance', OLD.Balance,
        'Status', OLD.Status
    ), JSON_OBJECT(
        'AccountID', NEW.AccountID,
        'Balance', NEW.Balance,
        'Status', NEW.Status
    ), 'system');
END$$

-- Trigger for transaction table audit
CREATE TRIGGER `tr_transaction_audit_insert` AFTER INSERT ON `transaction`
FOR EACH ROW
BEGIN
    INSERT INTO `audit_log` (`TableName`, `RecordID`, `Action`, `NewValues`, `UserType`)
    VALUES ('transaction', NEW.TransactionID, 'INSERT', JSON_OBJECT(
        'TransactionID', NEW.TransactionID,
        'AccountID', NEW.AccountID,
        'Amount', NEW.Amount,
        'Type', NEW.Type,
        'BalanceAfter', NEW.BalanceAfter
    ), 'system');
END$$
DELIMITER ;

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for customer account summary
CREATE OR REPLACE VIEW `v_customer_account_summary` AS
SELECT
    c.CustomerID,
    c.Name AS CustomerName,
    c.Email,
    c.Status AS CustomerStatus,
    COUNT(a.AccountID) AS TotalAccounts,
    SUM(a.Balance) AS TotalBalance,
    GROUP_CONCAT(DISTINCT a.AccountType) AS AccountTypes,
    MAX(a.LastTransactionDate) AS LastActivity
FROM customer c
LEFT JOIN account a ON c.CustomerID = a.CustomerID AND a.Status = 'active'
GROUP BY c.CustomerID, c.Name, c.Email, c.Status;

-- View for transaction history with account details
CREATE OR REPLACE VIEW `v_transaction_history` AS
SELECT
    t.TransactionID,
    t.TransactionNumber,
    t.Date,
    t.Type,
    t.Amount,
    t.Description,
    t.Status,
    a.AccountNumber,
    a.AccountType,
    c.Name AS CustomerName,
    c.Email AS CustomerEmail,
    b.BranchName
FROM transaction t
JOIN account a ON t.AccountID = a.AccountID
JOIN customer c ON a.CustomerID = c.CustomerID
JOIN branch b ON a.BranchID = b.BranchID
ORDER BY t.Date DESC;

-- View for account balances and details
CREATE OR REPLACE VIEW `v_account_details` AS
SELECT
    a.AccountID,
    a.AccountNumber,
    a.AccountType,
    a.Balance,
    a.MinimumBalance,
    a.Status AS AccountStatus,
    a.OpenDate,
    c.CustomerID,
    c.Name AS CustomerName,
    c.Email AS CustomerEmail,
    c.Phone AS CustomerPhone,
    b.BranchName,
    b.Location AS BranchLocation,
    (SELECT COUNT(*) FROM transaction WHERE AccountID = a.AccountID) AS TransactionCount,
    a.LastTransactionDate
FROM account a
JOIN customer c ON a.CustomerID = c.CustomerID
JOIN branch b ON a.BranchID = b.BranchID;

-- =====================================================
-- STORED PROCEDURES
-- =====================================================

-- Procedure to create a new account with validation
DELIMITER $$
CREATE PROCEDURE `sp_create_account`(
    IN p_customer_id INT,
    IN p_branch_id INT,
    IN p_account_type VARCHAR(50),
    IN p_initial_deposit DECIMAL(15,2),
    OUT p_account_id INT,
    OUT p_account_number VARCHAR(20),
    OUT p_result_message VARCHAR(255)
)
BEGIN
    DECLARE v_customer_status VARCHAR(20);
    DECLARE v_branch_status VARCHAR(20);
    DECLARE v_account_count INT;
    DECLARE v_new_account_number VARCHAR(20);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result_message = 'Error creating account';
        SET p_account_id = 0;
    END;

    START TRANSACTION;

    -- Validate customer
    SELECT Status INTO v_customer_status FROM customer WHERE CustomerID = p_customer_id;
    IF v_customer_status IS NULL THEN
        SET p_result_message = 'Customer not found';
        SET p_account_id = 0;
        ROLLBACK;
    ELSEIF v_customer_status != 'active' THEN
        SET p_result_message = 'Customer account is not active';
        SET p_account_id = 0;
        ROLLBACK;
    END IF;

    -- Validate branch
    SELECT Status INTO v_branch_status FROM branch WHERE BranchID = p_branch_id;
    IF v_branch_status IS NULL THEN
        SET p_result_message = 'Branch not found';
        SET p_account_id = 0;
        ROLLBACK;
    ELSEIF v_branch_status != 'active' THEN
        SET p_result_message = 'Branch is not active';
        SET p_account_id = 0;
        ROLLBACK;
    END IF;

    -- Generate account number
    SELECT COUNT(*) INTO v_account_count FROM account WHERE BranchID = p_branch_id;
    SET v_new_account_number = CONCAT(p_branch_id, LPAD(p_branch_id, 3, '0'), '-0000-', LPAD(v_account_count + 1, 4, '0'));

    -- Create account
    INSERT INTO account (CustomerID, BranchID, AccountNumber, AccountType, Balance, OpenDate)
    VALUES (p_customer_id, p_branch_id, v_new_account_number, p_account_type, p_initial_deposit, CURDATE());

    SET p_account_id = LAST_INSERT_ID();
    SET p_account_number = v_new_account_number;
    SET p_result_message = 'Account created successfully';

    -- Create initial transaction if deposit > 0
    IF p_initial_deposit > 0 THEN
        INSERT INTO transaction (AccountID, TransactionNumber, Amount, Type, Description, BalanceAfter)
        VALUES (p_account_id, CONCAT('TXN-', YEAR(NOW()), '-', LPAD(p_account_id, 6, '0')),
                p_initial_deposit, 'Deposit', 'Initial deposit', p_initial_deposit);
    END IF;

    COMMIT;
END$$
DELIMITER ;

-- =====================================================
-- FINAL CONFIGURATION
-- =====================================================

-- Set AUTO_INCREMENT values
ALTER TABLE `branch` AUTO_INCREMENT = 4;
ALTER TABLE `customer` AUTO_INCREMENT = 4;
ALTER TABLE `account` AUTO_INCREMENT = 106;
ALTER TABLE `transaction` AUTO_INCREMENT = 11;
ALTER TABLE `admin` AUTO_INCREMENT = 2;
ALTER TABLE `pending_account` AUTO_INCREMENT = 1;
ALTER TABLE `email_log` AUTO_INCREMENT = 1;
ALTER TABLE `verification_log` AUTO_INCREMENT = 1;
ALTER TABLE `audit_log` AUTO_INCREMENT = 1;
ALTER TABLE `system_settings` AUTO_INCREMENT = 11;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- =====================================================
-- SCHEMA CONSOLIDATION COMPLETE
-- =====================================================
-- This consolidated schema includes:
-- ✓ All core banking tables (branch, customer, account, transaction)
-- ✓ Administrative tables (admin, pending_account)
-- ✓ Audit and logging tables (email_log, verification_log, audit_log)
-- ✓ System configuration (system_settings)
-- ✓ Comprehensive foreign key relationships
-- ✓ Security constraints and validation rules
-- ✓ Performance indexes for common queries
-- ✓ Audit triggers for data tracking
-- ✓ Useful views for reporting
-- ✓ Stored procedures for common operations
-- ✓ Sample data for testing
-- =====================================================
