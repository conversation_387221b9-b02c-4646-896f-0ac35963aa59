<?php
/**
 * Banking System Setup Script
 * 
 * This script helps set up the banking system database and resolve connection issues.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Banking System Setup</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .code { background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; }
    button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
    button:hover { background: #005a87; }
</style>";

// Step 1: Test MySQL Connection
echo "<div class='section'>";
echo "<h2>Step 1: Testing MySQL Connection</h2>";

$host = "localhost";
$username = "root";
$password = "";

echo "<p class='info'>Attempting to connect to MySQL server...</p>";

// Try to connect without specifying a database
$conn = @new mysqli($host, $username, $password);

if ($conn->connect_error) {
    echo "<p class='error'>❌ MySQL Connection Failed: " . $conn->connect_error . "</p>";
    echo "<div class='section'>";
    echo "<h3>Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Check if XAMPP is running:</strong>";
    echo "<ul>";
    echo "<li>Open XAMPP Control Panel</li>";
    echo "<li>Start Apache and MySQL services</li>";
    echo "<li>Make sure MySQL shows 'Running' status</li>";
    echo "</ul></li>";
    echo "<li><strong>Check MySQL credentials:</strong>";
    echo "<ul>";
    echo "<li>Default XAMPP MySQL user: root</li>";
    echo "<li>Default XAMPP MySQL password: (empty)</li>";
    echo "<li>If you changed the password, update config/db_connect.php</li>";
    echo "</ul></li>";
    echo "<li><strong>Reset MySQL password (if needed):</strong>";
    echo "<div class='code'>mysqladmin -u root password \"\"</div>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    exit();
} else {
    echo "<p class='success'>✅ MySQL Connection Successful</p>";
}

// Step 2: Check if banking_system database exists
echo "<h3>Step 2: Checking Database</h3>";

$result = $conn->query("SHOW DATABASES LIKE 'banking_system'");
if ($result->num_rows > 0) {
    echo "<p class='success'>✅ Database 'banking_system' exists</p>";
    $database_exists = true;
} else {
    echo "<p class='warning'>⚠️ Database 'banking_system' does not exist</p>";
    $database_exists = false;
}

echo "</div>";

// Step 3: Database Setup Options
echo "<div class='section'>";
echo "<h2>Step 3: Database Setup Options</h2>";

if (!$database_exists) {
    echo "<p class='info'>The banking_system database needs to be created. Choose an option:</p>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='create_new'>Create New Database with Consolidated Schema</button>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='create_basic'>Create Basic Database (Original Schema)</button>";
    echo "</form>";
} else {
    echo "<p class='info'>Database exists. Choose an option:</p>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='migrate_consolidated'>Migrate to Consolidated Schema</button>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='test_current'>Test Current Database</button>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='reset_database'>Reset Database (WARNING: Deletes all data)</button>";
    echo "</form>";
}

echo "</div>";

// Handle form submissions
if ($_POST) {
    echo "<div class='section'>";
    echo "<h2>Processing Request...</h2>";
    
    if (isset($_POST['create_new'])) {
        createConsolidatedDatabase($conn);
    } elseif (isset($_POST['create_basic'])) {
        createBasicDatabase($conn);
    } elseif (isset($_POST['migrate_consolidated'])) {
        migrateToConsolidated($conn);
    } elseif (isset($_POST['test_current'])) {
        testCurrentDatabase($conn);
    } elseif (isset($_POST['reset_database'])) {
        resetDatabase($conn);
    }
    
    echo "</div>";
}

// Function to create consolidated database
function createConsolidatedDatabase($conn) {
    echo "<p class='info'>Creating new database with consolidated schema...</p>";
    
    // Read the consolidated schema file
    if (file_exists('consolidated_banking_system.sql')) {
        $sql_content = file_get_contents('consolidated_banking_system.sql');
        
        // Execute the SQL
        if ($conn->multi_query($sql_content)) {
            // Process all results
            do {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            } while ($conn->next_result());
            
            echo "<p class='success'>✅ Consolidated database created successfully!</p>";
            echo "<p class='info'>You can now access the banking system at: <a href='index.php'>index.php</a></p>";
        } else {
            echo "<p class='error'>❌ Error creating database: " . $conn->error . "</p>";
        }
    } else {
        echo "<p class='error'>❌ consolidated_banking_system.sql file not found</p>";
    }
}

// Function to create basic database
function createBasicDatabase($conn) {
    echo "<p class='info'>Creating basic database...</p>";
    
    // Create database
    if ($conn->query("CREATE DATABASE IF NOT EXISTS banking_system")) {
        echo "<p class='success'>✅ Database created</p>";
        
        // Select database
        $conn->select_db("banking_system");
        
        // Read setup file
        if (file_exists('setup_database.sql')) {
            $sql_content = file_get_contents('setup_database.sql');
            
            // Split into individual statements
            $statements = explode(';', $sql_content);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    if (!$conn->query($statement)) {
                        echo "<p class='error'>Error: " . $conn->error . "</p>";
                    }
                }
            }
            
            echo "<p class='success'>✅ Basic database setup completed!</p>";
            echo "<p class='info'>You can now access the banking system at: <a href='index.php'>index.php</a></p>";
        } else {
            echo "<p class='error'>❌ setup_database.sql file not found</p>";
        }
    } else {
        echo "<p class='error'>❌ Error creating database: " . $conn->error . "</p>";
    }
}

// Function to migrate to consolidated schema
function migrateToConsolidated($conn) {
    echo "<p class='info'>Migrating to consolidated schema...</p>";
    
    if (file_exists('migration_script.sql')) {
        $sql_content = file_get_contents('migration_script.sql');
        
        // Select database
        $conn->select_db("banking_system");
        
        if ($conn->multi_query($sql_content)) {
            // Process all results
            do {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            } while ($conn->next_result());
            
            echo "<p class='success'>✅ Migration completed successfully!</p>";
            echo "<p class='info'>You can now test the system at: <a href='test_consolidated_database.php'>test_consolidated_database.php</a></p>";
        } else {
            echo "<p class='error'>❌ Error during migration: " . $conn->error . "</p>";
        }
    } else {
        echo "<p class='error'>❌ migration_script.sql file not found</p>";
    }
}

// Function to test current database
function testCurrentDatabase($conn) {
    echo "<p class='info'>Testing current database...</p>";
    
    $conn->select_db("banking_system");
    
    // Check tables
    $result = $conn->query("SHOW TABLES");
    if ($result) {
        echo "<p class='success'>✅ Database connection working</p>";
        echo "<p class='info'>Tables found: " . $result->num_rows . "</p>";
        
        echo "<ul>";
        while ($row = $result->fetch_row()) {
            echo "<li>" . $row[0] . "</li>";
        }
        echo "</ul>";
        
        echo "<p class='info'>You can access the banking system at: <a href='index.php'>index.php</a></p>";
    } else {
        echo "<p class='error'>❌ Error accessing database: " . $conn->error . "</p>";
    }
}

// Function to reset database
function resetDatabase($conn) {
    echo "<p class='warning'>⚠️ Resetting database (this will delete all data)...</p>";
    
    if ($conn->query("DROP DATABASE IF EXISTS banking_system")) {
        echo "<p class='success'>✅ Database reset completed</p>";
        echo "<p class='info'>You can now create a new database using the options above.</p>";
    } else {
        echo "<p class='error'>❌ Error resetting database: " . $conn->error . "</p>";
    }
}

// Final instructions
echo "<div class='section'>";
echo "<h2>Additional Information</h2>";
echo "<p class='info'><strong>File Locations:</strong></p>";
echo "<ul>";
echo "<li><strong>Database Config:</strong> config/db_connect.php</li>";
echo "<li><strong>Consolidated Schema:</strong> consolidated_banking_system.sql</li>";
echo "<li><strong>Migration Script:</strong> migration_script.sql</li>";
echo "<li><strong>Test Script:</strong> test_consolidated_database.php</li>";
echo "</ul>";

echo "<p class='info'><strong>Default Login Credentials:</strong></p>";
echo "<ul>";
echo "<li><strong>Customer:</strong> <EMAIL> / **************</li>";
echo "<li><strong>Admin:</strong> admin / admin123</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>
