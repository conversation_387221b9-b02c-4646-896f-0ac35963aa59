<?php
/**
 * Database Consolidation Test Script
 * 
 * This script tests the consolidated banking system database
 * to ensure all tables, relationships, and constraints work correctly.
 * 
 * Version: 2.0
 * Created: 2025-02-07
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'config/db_connect.php';

echo "<h1>Banking System Database Consolidation Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

/**
 * Test function to run a query and check results
 */
function runTest($test_name, $query, $expected_result = null, $check_type = 'exists') {
    global $conn, $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    $result = $conn->query($query);
    
    if ($result === false) {
        $test_results[] = [
            'name' => $test_name,
            'status' => 'FAILED',
            'message' => 'Query failed: ' . $conn->error,
            'class' => 'error'
        ];
        return false;
    }
    
    $success = false;
    $message = '';
    
    switch ($check_type) {
        case 'exists':
            $success = $result->num_rows > 0;
            $message = $success ? "Found {$result->num_rows} records" : "No records found";
            break;
        case 'count':
            $row = $result->fetch_row();
            $count = $row[0];
            $success = ($expected_result === null) ? ($count > 0) : ($count == $expected_result);
            $message = "Count: $count" . ($expected_result ? " (expected: $expected_result)" : "");
            break;
        case 'value':
            $row = $result->fetch_row();
            $value = $row[0];
            $success = ($value == $expected_result);
            $message = "Value: $value (expected: $expected_result)";
            break;
    }
    
    if ($success) {
        $passed_tests++;
        $test_results[] = [
            'name' => $test_name,
            'status' => 'PASSED',
            'message' => $message,
            'class' => 'success'
        ];
    } else {
        $test_results[] = [
            'name' => $test_name,
            'status' => 'FAILED',
            'message' => $message,
            'class' => 'error'
        ];
    }
    
    return $success;
}

// Test 1: Database Connection
echo "<div class='section'>";
echo "<h2>Test 1: Database Connection</h2>";
if ($conn->connect_error) {
    echo "<p class='error'>Connection failed: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p class='success'>Database connection successful</p>";
}
echo "</div>";

// Test 2: Table Existence
echo "<div class='section'>";
echo "<h2>Test 2: Table Existence</h2>";

$required_tables = [
    'branch', 'customer', 'account', 'transaction', 'admin', 
    'pending_account', 'email_log', 'verification_log', 
    'audit_log', 'system_settings'
];

foreach ($required_tables as $table) {
    runTest("Table '$table' exists", "SHOW TABLES LIKE '$table'");
}
echo "</div>";

// Test 3: Foreign Key Constraints
echo "<div class='section'>";
echo "<h2>Test 3: Foreign Key Constraints</h2>";

$fk_tests = [
    "Account-Customer FK" => "SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'account' AND CONSTRAINT_NAME = 'fk_account_customer'",
    "Account-Branch FK" => "SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'account' AND CONSTRAINT_NAME = 'fk_account_branch'",
    "Transaction-Account FK" => "SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'transaction' AND CONSTRAINT_NAME = 'fk_transaction_account'",
    "Pending Account-Customer FK" => "SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'pending_account' AND CONSTRAINT_NAME = 'fk_pending_account_customer'"
];

foreach ($fk_tests as $test_name => $query) {
    runTest($test_name, $query, 1, 'count');
}
echo "</div>";

// Test 4: Data Integrity
echo "<div class='section'>";
echo "<h2>Test 4: Data Integrity</h2>";

// Check for orphaned records
runTest("No orphaned accounts", "SELECT COUNT(*) FROM account a LEFT JOIN customer c ON a.CustomerID = c.CustomerID WHERE c.CustomerID IS NULL", 0, 'count');
runTest("No orphaned transactions", "SELECT COUNT(*) FROM transaction t LEFT JOIN account a ON t.AccountID = a.AccountID WHERE a.AccountID IS NULL", 0, 'count');

// Check for negative balances
runTest("No negative account balances", "SELECT COUNT(*) FROM account WHERE Balance < 0", 0, 'count');

// Check for duplicate emails
runTest("No duplicate customer emails", "SELECT COUNT(*) - COUNT(DISTINCT Email) FROM customer", 0, 'count');
echo "</div>";

// Test 5: Sample Data
echo "<div class='section'>";
echo "<h2>Test 5: Sample Data</h2>";

runTest("Branches exist", "SELECT COUNT(*) FROM branch", null, 'count');
runTest("Customers exist", "SELECT COUNT(*) FROM customer", null, 'count');
runTest("Accounts exist", "SELECT COUNT(*) FROM account", null, 'count');
runTest("Transactions exist", "SELECT COUNT(*) FROM transaction", null, 'count');
runTest("System settings exist", "SELECT COUNT(*) FROM system_settings", null, 'count');
echo "</div>";

// Test 6: Account Number Generation
echo "<div class='section'>";
echo "<h2>Test 6: Account Number Format</h2>";

runTest("Account numbers follow format", "SELECT COUNT(*) FROM account WHERE AccountNumber REGEXP '^[0-9]+-[0-9]+-[0-9]+$'", null, 'count');
runTest("Unique account numbers", "SELECT COUNT(*) - COUNT(DISTINCT AccountNumber) FROM account", 0, 'count');
echo "</div>";

// Test 7: Transaction Numbers
echo "<div class='section'>";
echo "<h2>Test 7: Transaction Number Format</h2>";

runTest("Transaction numbers follow format", "SELECT COUNT(*) FROM transaction WHERE TransactionNumber REGEXP '^TXN-[0-9]+-[0-9]+$'", null, 'count');
runTest("Unique transaction numbers", "SELECT COUNT(*) - COUNT(DISTINCT TransactionNumber) FROM transaction", 0, 'count');
echo "</div>";

// Test 8: Views
echo "<div class='section'>";
echo "<h2>Test 8: Database Views</h2>";

$view_tests = [
    "Customer Account Summary View" => "SELECT COUNT(*) FROM v_customer_account_summary",
    "Transaction History View" => "SELECT COUNT(*) FROM v_transaction_history",
    "Account Details View" => "SELECT COUNT(*) FROM v_account_details"
];

foreach ($view_tests as $test_name => $query) {
    runTest($test_name, $query, null, 'count');
}
echo "</div>";

// Test 9: Constraints
echo "<div class='section'>";
echo "<h2>Test 9: Check Constraints</h2>";

// Test balance constraints
$test_query = "INSERT INTO account (CustomerID, BranchID, AccountNumber, Balance) VALUES (1, 1, 'TEST-NEGATIVE', -100.00)";
$result = $conn->query($test_query);
if ($result === false && strpos($conn->error, 'chk_balance_positive') !== false) {
    echo "<p class='success'>Balance constraint working: Negative balance rejected</p>";
} else {
    echo "<p class='error'>Balance constraint failed: Negative balance allowed</p>";
}

// Clean up test data
$conn->query("DELETE FROM account WHERE AccountNumber = 'TEST-NEGATIVE'");
echo "</div>";

// Test 10: System Settings
echo "<div class='section'>";
echo "<h2>Test 10: System Settings</h2>";

$settings_tests = [
    "Min account balance setting" => "SELECT COUNT(*) FROM system_settings WHERE SettingKey = 'min_account_balance'",
    "Max daily withdrawal setting" => "SELECT COUNT(*) FROM system_settings WHERE SettingKey = 'max_daily_withdrawal'",
    "Email verification setting" => "SELECT COUNT(*) FROM system_settings WHERE SettingKey = 'email_verification_required'"
];

foreach ($settings_tests as $test_name => $query) {
    runTest($test_name, $query, 1, 'count');
}
echo "</div>";

// Display Test Results Summary
echo "<div class='section'>";
echo "<h2>Test Results Summary</h2>";
echo "<table>";
echo "<tr><th>Test Name</th><th>Status</th><th>Details</th></tr>";

foreach ($test_results as $result) {
    echo "<tr>";
    echo "<td>{$result['name']}</td>";
    echo "<td class='{$result['class']}'>{$result['status']}</td>";
    echo "<td>{$result['message']}</td>";
    echo "</tr>";
}

echo "</table>";

$success_rate = round(($passed_tests / $total_tests) * 100, 2);
echo "<h3>Overall Results:</h3>";
echo "<p><strong>Total Tests:</strong> $total_tests</p>";
echo "<p><strong>Passed:</strong> <span class='success'>$passed_tests</span></p>";
echo "<p><strong>Failed:</strong> <span class='error'>" . ($total_tests - $passed_tests) . "</span></p>";
echo "<p><strong>Success Rate:</strong> <span class='" . ($success_rate >= 90 ? 'success' : 'error') . "'>$success_rate%</span></p>";

if ($success_rate >= 90) {
    echo "<h3 class='success'>✅ Database consolidation test PASSED!</h3>";
    echo "<p class='info'>The consolidated banking system database is working correctly and ready for use.</p>";
} else {
    echo "<h3 class='error'>❌ Database consolidation test FAILED!</h3>";
    echo "<p class='error'>Please review the failed tests and fix any issues before using the system.</p>";
}
echo "</div>";

// Display Database Statistics
echo "<div class='section'>";
echo "<h2>Database Statistics</h2>";

$stats_queries = [
    "Total Branches" => "SELECT COUNT(*) FROM branch",
    "Total Customers" => "SELECT COUNT(*) FROM customer",
    "Active Customers" => "SELECT COUNT(*) FROM customer WHERE Status = 'active'",
    "Total Accounts" => "SELECT COUNT(*) FROM account",
    "Active Accounts" => "SELECT COUNT(*) FROM account WHERE Status = 'active'",
    "Total Transactions" => "SELECT COUNT(*) FROM transaction",
    "Total Balance" => "SELECT FORMAT(SUM(Balance), 2) FROM account WHERE Status = 'active'",
    "Average Balance" => "SELECT FORMAT(AVG(Balance), 2) FROM account WHERE Status = 'active'"
];

echo "<table>";
echo "<tr><th>Metric</th><th>Value</th></tr>";

foreach ($stats_queries as $metric => $query) {
    $result = $conn->query($query);
    if ($result && $row = $result->fetch_row()) {
        echo "<tr><td>$metric</td><td>{$row[0]}</td></tr>";
    }
}

echo "</table>";
echo "</div>";

// Close database connection
$conn->close();

echo "<div class='section'>";
echo "<h2>Test Complete</h2>";
echo "<p class='info'>Database consolidation testing completed. Review the results above to ensure everything is working correctly.</p>";
echo "</div>";
?>
