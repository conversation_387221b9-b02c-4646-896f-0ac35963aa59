# Banking System Database Documentation

## Overview

This document provides comprehensive documentation for the consolidated Ferrer Banking System database. The database has been designed to support a complete banking system with enhanced security, audit trails, and performance optimizations.

## Database Schema Version

- **Version**: 2.0
- **Created**: 2025-02-07
- **Database Name**: `banking_system`
- **Character Set**: utf8mb4
- **Collation**: utf8mb4_general_ci

## Table Structure

### Core Banking Tables

#### 1. `branch` - Bank Branch Information
Stores information about bank branches and their operational details.

**Columns:**
- `BranchID` (INT, PK, AUTO_INCREMENT) - Unique branch identifier
- `BranchName` (VARCHAR(100), NOT NULL) - Name of the branch
- `Location` (VARCHAR(255), NOT NULL) - Physical location/address
- `BranchCode` (VARCHAR(10), UNIQUE) - Unique branch code for identification
- `Phone` (VARCHAR(15)) - Branch contact phone number
- `Email` (VARCHAR(100)) - Branch email address
- `Manager` (VARCHAR(100)) - Branch manager name
- `Status` (ENUM: 'active', 'inactive', 'maintenance', DEFAULT 'active') - Branch operational status
- `CreatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP) - Record creation timestamp
- `UpdatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE) - Last update timestamp

**Indexes:**
- Primary Key: `BranchID`
- Index: `idx_branch_status` on `Status`
- Index: `idx_branch_code` on `BranchCode`

**Relationships:**
- Referenced by: `account`, `pending_account`

#### 2. `customer` - Customer Information
Stores customer personal information and account status with enhanced security features.

**Columns:**
- `CustomerID` (INT, PK, AUTO_INCREMENT) - Unique customer identifier
- `Name` (VARCHAR(100), NOT NULL) - Customer full name
- `Phone` (VARCHAR(15), NOT NULL, UNIQUE) - Customer phone number
- `Email` (VARCHAR(100), NOT NULL, UNIQUE) - Customer email address
- `Address` (TEXT, NOT NULL) - Customer address
- `DateOfBirth` (DATE) - Customer date of birth
- `NationalID` (VARCHAR(50), UNIQUE) - National identification number
- `Status` (ENUM: 'pending', 'active', 'suspended', 'closed', DEFAULT 'pending') - Account status
- `VerificationCode` (VARCHAR(32)) - Email verification code
- `VerificationExpiry` (DATETIME) - Verification code expiry time
- `IsVerified` (TINYINT(1), DEFAULT 0) - Email verification status
- `RegistrationDate` (DATETIME, DEFAULT CURRENT_TIMESTAMP) - Account registration date
- `LastLoginDate` (DATETIME) - Last successful login
- `FailedLoginAttempts` (INT(3), DEFAULT 0) - Failed login attempt counter
- `AccountLockUntil` (DATETIME) - Account lock expiry time
- `CreatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP) - Record creation timestamp
- `UpdatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE) - Last update timestamp

**Constraints:**
- Phone number must be at least 10 characters
- Email must match valid email format regex
- Unique constraints on email, phone, and national ID

**Indexes:**
- Primary Key: `CustomerID`
- Unique: `unique_email`, `unique_phone`, `unique_national_id`
- Index: `idx_customer_status`, `idx_customer_email`, `idx_customer_verification`, `idx_customer_registration`

**Relationships:**
- Referenced by: `account`, `pending_account`, `email_log`, `verification_log`

#### 3. `account` - Customer Bank Accounts
Stores customer account information with enhanced features for different account types.

**Columns:**
- `AccountID` (INT, PK, AUTO_INCREMENT) - Unique account identifier
- `CustomerID` (INT, NOT NULL, FK) - Reference to customer
- `BranchID` (INT, NOT NULL, FK) - Reference to branch
- `AccountNumber` (VARCHAR(20), NOT NULL, UNIQUE) - Unique account number
- `AccountType` (ENUM: 'Savings', 'Checking', 'Investment', 'Business', 'Student', DEFAULT 'Savings') - Account type
- `Balance` (DECIMAL(15,2), NOT NULL, DEFAULT 0.00) - Current account balance
- `MinimumBalance` (DECIMAL(15,2), NOT NULL, DEFAULT 100.00) - Minimum required balance
- `InterestRate` (DECIMAL(5,4), DEFAULT 0.0000) - Interest rate for the account
- `Status` (ENUM: 'active', 'inactive', 'frozen', 'closed', DEFAULT 'active') - Account status
- `OpenDate` (DATE, NOT NULL, DEFAULT CURRENT_DATE) - Account opening date
- `CloseDate` (DATE) - Account closing date (if applicable)
- `LastTransactionDate` (DATETIME) - Date of last transaction
- `CreatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP) - Record creation timestamp
- `UpdatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE) - Last update timestamp

**Constraints:**
- Balance must be non-negative
- Minimum balance must be non-negative
- Interest rate must be between 0 and 1
- Close date must be after open date

**Indexes:**
- Primary Key: `AccountID`
- Unique: `unique_account_number`
- Foreign Keys: `idx_customer_id`, `idx_branch_id`
- Index: `idx_account_type`, `idx_account_status`, `idx_balance`

**Relationships:**
- References: `customer` (CustomerID), `branch` (BranchID)
- Referenced by: `transaction`

#### 4. `transaction` - Financial Transactions
Stores all financial transactions with comprehensive audit trail and enhanced features.

**Columns:**
- `TransactionID` (INT, PK, AUTO_INCREMENT) - Unique transaction identifier
- `AccountID` (INT, NOT NULL, FK) - Reference to account
- `TransactionNumber` (VARCHAR(30), NOT NULL, UNIQUE) - Unique transaction number
- `Amount` (DECIMAL(15,2), NOT NULL) - Transaction amount
- `Type` (ENUM: 'Credit', 'Debit', 'Transfer', 'Deposit', 'Withdrawal', 'Interest', 'Fee', NOT NULL) - Transaction type
- `Description` (VARCHAR(255)) - Transaction description
- `ReferenceNumber` (VARCHAR(50)) - External reference number
- `RelatedAccountID` (INT, FK) - Related account for transfers
- `Date` (DATETIME, NOT NULL, DEFAULT CURRENT_TIMESTAMP) - Transaction date and time
- `ProcessedBy` (VARCHAR(100), DEFAULT 'SYSTEM') - Who processed the transaction
- `Status` (ENUM: 'pending', 'completed', 'failed', 'cancelled', DEFAULT 'completed') - Transaction status
- `BalanceAfter` (DECIMAL(15,2), NOT NULL) - Account balance after transaction
- `CreatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP) - Record creation timestamp
- `UpdatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE) - Last update timestamp

**Constraints:**
- Amount cannot be zero
- Balance after transaction must be non-negative

**Indexes:**
- Primary Key: `TransactionID`
- Unique: `unique_transaction_number`
- Foreign Keys: `idx_account_id`, `idx_related_account_id`
- Index: `idx_transaction_date`, `idx_transaction_type`, `idx_transaction_status`, `idx_amount`

**Relationships:**
- References: `account` (AccountID), `account` (RelatedAccountID)

### Administrative Tables

#### 5. `admin` - System Administrators
Stores administrator account information with role-based access control.

**Columns:**
- `AdminID` (INT, PK, AUTO_INCREMENT) - Unique admin identifier
- `Username` (VARCHAR(50), NOT NULL, UNIQUE) - Admin username
- `Password` (VARCHAR(255), NOT NULL) - Hashed password
- `Name` (VARCHAR(100), NOT NULL) - Admin full name
- `Email` (VARCHAR(100), NOT NULL, UNIQUE) - Admin email address
- `Role` (ENUM: 'super_admin', 'admin', 'manager', 'operator', DEFAULT 'admin') - Admin role
- `Status` (ENUM: 'active', 'inactive', 'suspended', DEFAULT 'active') - Admin status
- `LastLogin` (DATETIME) - Last login timestamp
- `LoginAttempts` (INT(3), DEFAULT 0) - Failed login attempts
- `LockUntil` (DATETIME) - Account lock expiry
- `PasswordChangedAt` (DATETIME, DEFAULT CURRENT_TIMESTAMP) - Last password change
- `CreatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP) - Record creation timestamp
- `UpdatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE) - Last update timestamp

**Constraints:**
- Username must be at least 3 characters
- Password must be at least 8 characters

**Indexes:**
- Primary Key: `AdminID`
- Unique: `unique_username`, `unique_admin_email`
- Index: `idx_admin_status`, `idx_admin_role`

**Relationships:**
- Referenced by: `pending_account` (ApprovedBy)

#### 6. `pending_account` - Account Requests
Stores new account requests awaiting approval with workflow management.

**Columns:**
- `RequestID` (INT, PK, AUTO_INCREMENT) - Unique request identifier
- `CustomerID` (INT, NOT NULL, FK) - Reference to customer
- `AccountType` (ENUM: 'Savings', 'Checking', 'Investment', 'Business', 'Student', DEFAULT 'Savings') - Requested account type
- `BranchID` (INT, NOT NULL, FK) - Reference to branch
- `InitialDeposit` (DECIMAL(15,2), NOT NULL, DEFAULT 0.00) - Initial deposit amount
- `RequestDate` (DATETIME, DEFAULT CURRENT_TIMESTAMP) - Request submission date
- `Status` (ENUM: 'pending', 'approved', 'rejected', 'cancelled', DEFAULT 'pending') - Request status
- `ApprovedBy` (INT, FK) - Admin who approved/rejected
- `ApprovalDate` (DATETIME) - Approval/rejection date
- `RejectionReason` (TEXT) - Reason for rejection
- `Notes` (TEXT) - Additional notes
- `CreatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP) - Record creation timestamp
- `UpdatedAt` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE) - Last update timestamp

**Constraints:**
- Initial deposit must be non-negative

**Indexes:**
- Primary Key: `RequestID`
- Foreign Keys: `idx_customer_id_pending`, `idx_branch_id_pending`, `idx_approved_by`
- Index: `idx_request_status`, `idx_request_date`

**Relationships:**
- References: `customer` (CustomerID), `branch` (BranchID), `admin` (ApprovedBy)

### Audit and Logging Tables

#### 7. `email_log` - Email Activity Log
Tracks all email communications for audit and debugging purposes.

#### 8. `verification_log` - Verification Activity Log
Tracks email verification activities for security monitoring.

#### 9. `audit_log` - System Audit Log
Comprehensive audit trail for all system activities and data changes.

#### 10. `system_settings` - System Configuration
Centralized system configuration and parameter management.

## Foreign Key Relationships

The database maintains referential integrity through the following foreign key relationships:

1. **account → customer**: Each account belongs to one customer
2. **account → branch**: Each account is associated with one branch
3. **transaction → account**: Each transaction belongs to one account
4. **transaction → account** (related): Transfer transactions reference another account
5. **pending_account → customer**: Each request is from one customer
6. **pending_account → branch**: Each request is for one branch
7. **pending_account → admin**: Each approval is by one admin
8. **email_log → customer**: Each email log entry relates to one customer
9. **verification_log → customer**: Each verification log entry relates to one customer

## Security Features

1. **Data Validation**: Check constraints ensure data integrity
2. **Unique Constraints**: Prevent duplicate critical data
3. **Foreign Key Constraints**: Maintain referential integrity
4. **Audit Trails**: Complete logging of all activities
5. **Email Verification**: Customer email verification system
6. **Account Locking**: Failed login attempt protection
7. **Password Security**: Hashed password storage
8. **Role-Based Access**: Different admin roles with varying permissions

## Performance Optimizations

1. **Strategic Indexing**: Indexes on frequently queried columns
2. **Composite Indexes**: Multi-column indexes for complex queries
3. **Proper Data Types**: Optimized data types for storage efficiency
4. **Query Optimization**: Views for common complex queries

## Usage Guidelines

1. **Always use transactions** for financial operations
2. **Validate data** before insertion using constraints
3. **Monitor audit logs** for security and compliance
4. **Regular backup** of critical data
5. **Use prepared statements** to prevent SQL injection
6. **Follow the principle of least privilege** for admin access
